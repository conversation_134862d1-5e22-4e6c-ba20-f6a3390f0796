import React from "react";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { LogBox, View, Text } from "react-native";
import { NavigationContainer } from "@react-navigation/native";

// Import providers
import { AuthProvider } from "./src/context/AuthContext";
import { VoiceProvider } from "./src/context/VoiceContext";
import { CartProvider } from "./src/context/CartContext";

// Import navigation
import AppNavigator from "./src/navigation/AppNavigator";

// Ignore specific warnings
LogBox.ignoreLogs([
  "AsyncStorage has been extracted from react-native core",
  "VirtualizedLists should never be nested",
  "Expo AV has been deprecated",
  "TurboModuleRegistry.getEnforcing",
  "DeviceInfo",
  "runtime not ready",
]);

// Navigation error handling
const navigationRef = React.createRef();

const onNavigationStateChange = (state) => {
  // Optional: Add navigation state change handling here
};

export default function App() {
  return (
    <SafeAreaProvider>
      <StatusBar style='dark' />
      <AuthProvider>
        <VoiceProvider>
          <CartProvider>
            <NavigationContainer
              ref={navigationRef}
              onStateChange={onNavigationStateChange}
              fallback={<StatusBar style='dark' />}
            >
              <AppNavigator />
            </NavigationContainer>
          </CartProvider>
        </VoiceProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}

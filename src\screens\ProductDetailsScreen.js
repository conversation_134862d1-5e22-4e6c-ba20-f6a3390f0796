import React, { useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, ROUTES } from "../utils/constants";
import VoiceButton from "../components/VoiceButton";
import TranscriptDisplay from "../components/TranscriptDisplay";
import { useVoice } from "../context/VoiceContext";
import { useCart } from "../context/CartContext";

const ProductDetailsScreen = ({ route, navigation }) => {
  const { product } = route.params || {};
  const { selectProduct, speak } = useVoice();
  const { addItem, getItemCount } = useCart();

  // Select product for voice commands and announce product details
  useEffect(() => {
    if (product) {
      selectProduct(product);
      speak(
        `${product.name}. Price: ${product.currency} ${product.price}. ${product.description}`
      );
    }
  }, [product]);

  // Handle add to cart
  const handleAddToCart = async () => {
    if (!product) return;

    try {
      await addItem(product, 1);
      speak(`Added ${product.name} to cart`);
    } catch (error) {
      console.error("Error adding to cart:", error);
    }
  };

  // Navigate to cart
  const navigateToCart = () => {
    try {
      navigation.navigate(ROUTES.CART);
    } catch (error) {
      console.error("Navigation error:", error);
    }
  };

  if (!product) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Product not found</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            try {
              navigation.goBack();
            } catch (error) {
              console.error("Navigation error:", error);
            }
          }}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Product Image */}
        <Image
          source={{ uri: product.imageUrl }}
          style={styles.image}
          resizeMode='cover'
        />

        {/* Product Info */}
        <View style={styles.infoContainer}>
          <Text style={styles.name}>{product.name}</Text>
          <Text style={styles.price}>
            {product.currency} {product.price.toFixed(2)}
          </Text>

          {!product.inStock && (
            <View style={styles.outOfStockBadge}>
              <Text style={styles.outOfStockText}>Out of Stock</Text>
            </View>
          )}

          <Text style={styles.description}>{product.description}</Text>

          {/* Tags */}
          <View style={styles.tagsContainer}>
            {product.tags &&
              product.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
          </View>
        </View>
      </ScrollView>

      {/* Bottom Bar */}
      <View style={styles.bottomBar}>
        <TouchableOpacity
          style={styles.cartIconContainer}
          onPress={navigateToCart}
        >
          <MaterialIcons name='shopping-cart' size={24} color={COLORS.TEXT} />
          {getItemCount() > 0 && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{getItemCount()}</Text>
            </View>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.addToCartButton,
            !product.inStock && styles.disabledButton,
          ]}
          onPress={handleAddToCart}
          disabled={!product.inStock}
        >
          <MaterialIcons name='add-shopping-cart' size={20} color='#FFFFFF' />
          <Text style={styles.addToCartText}>
            {product.inStock ? "Add to Cart" : "Out of Stock"}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Voice Button */}
      <View style={styles.voiceButtonContainer}>
        <VoiceButton />
      </View>

      {/* Transcript Display */}
      <TranscriptDisplay />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: COLORS.ERROR,
    marginBottom: 20,
  },
  backButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.PRIMARY,
    borderRadius: 5,
  },
  backButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  image: {
    width: "100%",
    height: 300,
  },
  infoContainer: {
    padding: 15,
  },
  name: {
    fontSize: 24,
    fontWeight: "bold",
    color: COLORS.TEXT,
    marginBottom: 10,
  },
  price: {
    fontSize: 22,
    fontWeight: "bold",
    color: COLORS.PRIMARY,
    marginBottom: 15,
  },
  outOfStockBadge: {
    backgroundColor: COLORS.ERROR,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 5,
    alignSelf: "flex-start",
    marginBottom: 15,
  },
  outOfStockText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
  },
  description: {
    fontSize: 16,
    color: COLORS.TEXT,
    lineHeight: 24,
    marginBottom: 20,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 20,
  },
  tag: {
    backgroundColor: "#F0F0F0",
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    marginRight: 10,
    marginBottom: 10,
  },
  tagText: {
    fontSize: 14,
    color: COLORS.LIGHT_TEXT,
  },
  bottomBar: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    backgroundColor: "#FFFFFF",
  },
  cartIconContainer: {
    padding: 10,
    marginRight: 15,
  },
  badge: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: COLORS.SECONDARY,
    borderRadius: 10,
    width: 18,
    height: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  badgeText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "bold",
  },
  addToCartButton: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: 12,
    borderRadius: 5,
  },
  disabledButton: {
    backgroundColor: COLORS.LIGHT_TEXT,
  },
  addToCartText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 10,
  },
  voiceButtonContainer: {
    position: "absolute",
    bottom: 80,
    right: 20,
  },
});

export default ProductDetailsScreen;

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS } from "../utils/constants";
import ProductCard from "../components/ProductCard";
import VoiceButton from "../components/VoiceButton";
import TranscriptDisplay from "../components/TranscriptDisplay";
import productService from "../services/productService";
import { useVoice } from "../context/VoiceContext";

const SearchScreen = ({ route, navigation }) => {
  const { query, results: initialResults } = route.params || {};
  const [results, setResults] = useState(initialResults || []);
  const [loading, setLoading] = useState(!initialResults);

  const { speak } = useVoice();

  // Load search results if not provided
  useEffect(() => {
    const loadResults = async () => {
      if (!query) return;

      setLoading(true);
      try {
        const searchResults = await productService.searchProducts(query);
        setResults(searchResults);

        // Announce results
        if (searchResults.length > 0) {
          speak(`Found ${searchResults.length} products for ${query}`);
        } else {
          speak(`No products found for ${query}`);
        }
      } catch (error) {
        console.error("Error searching products:", error);
      } finally {
        setLoading(false);
      }
    };

    if (!initialResults && query) {
      loadResults();
    } else if (initialResults) {
      // Announce results
      if (initialResults.length > 0) {
        speak(`Found ${initialResults.length} products for ${query}`);
      } else {
        speak(`No products found for ${query}`);
      }
    }
  }, [query, initialResults]);

  // Render product item
  const renderProductItem = ({ item }) => (
    <ProductCard product={item} horizontal={true} />
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Search Query */}
      <View style={styles.queryContainer}>
        <Text style={styles.queryLabel}>Search results for:</Text>
        <Text style={styles.queryText}>{query}</Text>
      </View>

      {/* Results */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={COLORS.PRIMARY} />
        </View>
      ) : (
        <FlatList
          data={results}
          renderItem={renderProductItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.resultsList}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <MaterialIcons
                name='search-off'
                size={48}
                color={COLORS.LIGHT_TEXT}
              />
              <Text style={styles.emptyText}>No products found</Text>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <Text style={styles.backButtonText}>Back to Home</Text>
              </TouchableOpacity>
            </View>
          }
        />
      )}

      {/* Voice Button */}
      <View style={styles.voiceButtonContainer}>
        <VoiceButton />
      </View>

      {/* Transcript Display */}
      <TranscriptDisplay />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  queryContainer: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  queryLabel: {
    fontSize: 14,
    color: COLORS.LIGHT_TEXT,
  },
  queryText: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.TEXT,
    marginTop: 5,
  },
  resultsList: {
    padding: 15,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 50,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.LIGHT_TEXT,
    marginTop: 10,
    marginBottom: 20,
  },
  backButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.PRIMARY,
    borderRadius: 5,
  },
  backButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  voiceButtonContainer: {
    position: "absolute",
    bottom: 20,
    right: 20,
  },
});

export default SearchScreen;

# IHearVoices - Voice-Activated Shopping Assistant

IHearVoices is a React Native mobile application that allows users to search for products, add them to cart, and complete checkout using voice commands. The app integrates with Paystack Ghana for payment processing.

## Features

- **Voice Command Recognition**: Search for products, add to cart, view cart, and checkout using voice commands
- **Product Search**: Find products by name, category, or description
- **Cart Management**: Add, remove, and update quantities of items in your cart
- **User Authentication**: Register, login, and manage your profile
- **Checkout Flow**: Complete the shopping experience with Paystack payment integration
- **Voice Feedback**: Receive voice responses for your actions

## Voice Commands

The app supports the following voice commands:
- **Search**: "search for [product]", "find [product]", "look for [product]"
- **Add to Cart**: "add", "put in cart", "buy", "purchase"
- **View Cart**: "view cart", "show cart", "see cart", "my cart"
- **Checkout**: "checkout", "proceed to checkout", "pay", "payment"
- **Confirm**: "confirm", "yes", "proceed", "continue"
- **Cancel**: "cancel", "no", "stop", "back"

## Setup Instructions

### Prerequisites
- Node.js (v14 or higher)
- Expo CLI (`npm install -g expo-cli`)
- Expo Go app on your mobile device or an Android/iOS emulator

### Installation
1. Clone the repository
   ```
   git clone https://github.com/yourusername/IHearVoices.git
   cd IHearVoices
   ```

2. Install dependencies
   ```
   npm install
   ```

3. Configure Paystack
   - Open `src/config/paystack.js`
   - Replace `YOUR_PAYSTACK_PUBLIC_KEY` with your actual Paystack public key

4. Start the development server
   ```
   npm start
   ```

5. Scan the QR code with the Expo Go app on your device or run on an emulator

## Project Structure

```
IHearVoices/
├── App.js                 # App entry point
├── app.json               # Expo configuration
├── assets/                # App assets (images, fonts)
├── src/
│   ├── assets/            # Application assets
│   ├── components/        # Reusable UI components
│   ├── config/            # Configuration files
│   ├── context/           # React Context providers
│   ├── hooks/             # Custom React hooks
│   ├── navigation/        # Navigation configuration
│   ├── screens/           # App screens
│   ├── services/          # Business logic services
│   └── utils/             # Utility functions and constants
```

## Development Notes

- The app currently uses mock data for products
- Speech recognition is simulated for development purposes
- AsyncStorage is used for persistence

## Future Improvements

- Implement real speech recognition API integration
- Add backend API for product data
- Enhance voice command processing with NLP
- Add more payment options
- Implement order history and tracking

## License

[MIT License](LICENSE) 
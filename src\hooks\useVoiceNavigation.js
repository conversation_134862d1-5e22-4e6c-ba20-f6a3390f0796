import { useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useVoice } from '../context/VoiceContext';
import { ROUTES } from '../utils/constants';

/**
 * Custom hook to set up voice navigation callback
 * This should be used in screens that need voice navigation support
 */
export const useVoiceNavigation = () => {
  const navigation = useNavigation();
  const { setNavigationCallback } = useVoice();

  useEffect(() => {
    if (!navigation) {
      console.warn('Navigation object is not available');
      return;
    }

    // Set up the navigation callback for voice commands
    const navigateCallback = (routeName, params) => {
      try {
        if (!routeName) {
          console.warn('Navigation attempted with null/undefined route');
          return;
        }

        // Validate the route exists
        const validRoutes = Object.values(ROUTES);
        if (typeof routeName === 'string' && !validRoutes.includes(routeName)) {
          console.warn(`Invalid route name: ${routeName}`);
          return;
        }

        if (typeof routeName === 'string') {
          navigation.navigate(routeName, params || {});
        } else if (typeof routeName === 'object') {
          // Ensure the object has a name property when navigating with an object
          if (!routeName.name) {
            console.warn('Navigation object must have a name property');
            return;
          }
          
          // Validate the route name in the object
          if (!validRoutes.includes(routeName.name)) {
            console.warn(`Invalid route name in object: ${routeName.name}`);
            return;
          }
          
          navigation.navigate(routeName);
        } else {
          console.warn('Invalid navigation parameter type:', typeof routeName);
        }
      } catch (error) {
        console.error('Navigation error:', error);
      }
    };

    setNavigationCallback(navigateCallback);

    // Cleanup on unmount
    return () => {
      setNavigationCallback(null);
    };
  }, [navigation, setNavigationCallback]);
};

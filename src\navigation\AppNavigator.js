import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { ROUTES } from "../utils/constants";

// Import screens
import HomeScreen from "../screens/HomeScreen";
import SearchScreen from "../screens/SearchScreen";
import ProductDetailsScreen from "../screens/ProductDetailsScreen";
import CartScreen from "../screens/CartScreen";
import CheckoutScreen from "../screens/CheckoutScreen";
import PaymentScreen from "../screens/PaymentScreen";
import SuccessScreen from "../screens/SuccessScreen";
import ProfileScreen from "../screens/ProfileScreen";
import LoginScreen from "../screens/LoginScreen";
import RegisterScreen from "../screens/RegisterScreen";

// Import contexts
import { useAuth } from "../context/AuthContext";

// Create stack navigators
const MainStack = createStackNavigator();
const AuthStack = createStackNavigator();

// Auth navigator for unauthenticated users
const AuthNavigator = () => {
  return (
    <AuthStack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <AuthStack.Screen name={ROUTES.LOGIN} component={LoginScreen} />
      <AuthStack.Screen name={ROUTES.REGISTER} component={RegisterScreen} />
    </AuthStack.Navigator>
  );
};

// Main app navigator for authenticated users
const MainNavigator = () => {
  return (
    <MainStack.Navigator
      initialRouteName={ROUTES.HOME}
      screenOptions={{
        headerStyle: {
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 0,
        },
      }}
    >
      <MainStack.Screen
        name={ROUTES.HOME}
        component={HomeScreen}
        options={{ title: "IHearVoices" }}
      />
      <MainStack.Screen
        name={ROUTES.SEARCH}
        component={SearchScreen}
        options={{ title: "Search Results" }}
      />
      <MainStack.Screen
        name={ROUTES.PRODUCT_DETAILS}
        component={ProductDetailsScreen}
        options={({ route }) => ({
          title: route.params?.product?.name || "Product Details",
        })}
      />
      <MainStack.Screen
        name={ROUTES.CART}
        component={CartScreen}
        options={{ title: "Shopping Cart" }}
      />
      <MainStack.Screen
        name={ROUTES.CHECKOUT}
        component={CheckoutScreen}
        options={{ title: "Checkout" }}
      />
      <MainStack.Screen
        name={ROUTES.PAYMENT}
        component={PaymentScreen}
        options={{ title: "Payment" }}
      />
      <MainStack.Screen
        name={ROUTES.SUCCESS}
        component={SuccessScreen}
        options={{ title: "Order Successful", headerShown: false }}
      />
      <MainStack.Screen
        name={ROUTES.PROFILE}
        component={ProfileScreen}
        options={{ title: "My Profile" }}
      />
      <MainStack.Screen
        name={ROUTES.LOGIN}
        component={LoginScreen}
        options={{ title: "Login" }}
      />
      <MainStack.Screen
        name={ROUTES.REGISTER}
        component={RegisterScreen}
        options={{ title: "Register" }}
      />
    </MainStack.Navigator>
  );
};

// Root navigator that handles auth state
const AppNavigator = () => {
  const { isLoggedIn, loading } = useAuth();

  // Show main app for all users for now, regardless of login state
  // In a real app with required authentication, you would conditionally
  // render MainNavigator or AuthNavigator based on isLoggedIn()
  return <MainNavigator />;
};

export default AppNavigator;

import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, ROUTES } from "../utils/constants";
import { useAuth } from "../context/AuthContext";

const RegisterScreen = ({ route, navigation }) => {
  const { returnTo } = route.params || {};
  const { register, loading, error } = useAuth();

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    phone: "",
    address: {
      street: "",
      city: "",
      region: "",
      country: "Ghana",
    },
  });

  const [formErrors, setFormErrors] = useState({});

  // Handle input change
  const handleInputChange = (field, value) => {
    if (field.includes(".")) {
      // Handle nested fields (address)
      const [parent, child] = field.split(".");
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent],
          [child]: value,
        },
      });
    } else {
      // Handle top-level fields
      setFormData({
        ...formData,
        [field]: value,
      });
    }

    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors({
        ...formErrors,
        [field]: null,
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Email is invalid";
    }

    if (!formData.password.trim()) {
      errors.password = "Password is required";
    } else if (formData.password.length < 6) {
      errors.password = "Password must be at least 6 characters";
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
    }

    if (!formData.phone.trim()) {
      errors.phone = "Phone number is required";
    }

    if (!formData.address.street.trim()) {
      errors["address.street"] = "Street address is required";
    }

    if (!formData.address.city.trim()) {
      errors["address.city"] = "City is required";
    }

    if (!formData.address.region.trim()) {
      errors["address.region"] = "Region is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle registration
  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Remove confirmPassword from data sent to register
      const { confirmPassword, ...registerData } = formData;

      await register(registerData);

      Alert.alert(
        "Registration Successful",
        "Your account has been created successfully.",
        [
          {
            text: "OK",
            onPress: () => {
              // Navigate to returnTo or Home
              if (returnTo) {
                navigation.navigate(returnTo);
              } else {
                navigation.navigate(ROUTES.HOME);
              }
            },
          },
        ]
      );
    } catch (err) {
      Alert.alert("Registration Failed", err.message);
    }
  };

  // Navigate to login screen
  const handleLogin = () => {
    navigation.navigate(ROUTES.LOGIN, { returnTo });
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Register</Text>
            <Text style={styles.subtitle}>Create a new account</Text>
          </View>

          <View style={styles.form}>
            {/* Personal Information */}
            <Text style={styles.sectionTitle}>Personal Information</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Full Name</Text>
              <View style={styles.inputContainer}>
                <MaterialIcons
                  name='person'
                  size={20}
                  color={COLORS.LIGHT_TEXT}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  value={formData.name}
                  onChangeText={(text) => handleInputChange("name", text)}
                  placeholder='Enter your full name'
                />
              </View>
              {formErrors.name && (
                <Text style={styles.errorText}>{formErrors.name}</Text>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Email</Text>
              <View style={styles.inputContainer}>
                <MaterialIcons
                  name='email'
                  size={20}
                  color={COLORS.LIGHT_TEXT}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  value={formData.email}
                  onChangeText={(text) => handleInputChange("email", text)}
                  placeholder='Enter your email'
                  keyboardType='email-address'
                  autoCapitalize='none'
                />
              </View>
              {formErrors.email && (
                <Text style={styles.errorText}>{formErrors.email}</Text>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Phone Number</Text>
              <View style={styles.inputContainer}>
                <MaterialIcons
                  name='phone'
                  size={20}
                  color={COLORS.LIGHT_TEXT}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  value={formData.phone}
                  onChangeText={(text) => handleInputChange("phone", text)}
                  placeholder='Enter your phone number'
                  keyboardType='phone-pad'
                />
              </View>
              {formErrors.phone && (
                <Text style={styles.errorText}>{formErrors.phone}</Text>
              )}
            </View>

            {/* Password */}
            <Text style={styles.sectionTitle}>Password</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Password</Text>
              <View style={styles.inputContainer}>
                <MaterialIcons
                  name='lock'
                  size={20}
                  color={COLORS.LIGHT_TEXT}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  value={formData.password}
                  onChangeText={(text) => handleInputChange("password", text)}
                  placeholder='Enter your password'
                  secureTextEntry
                />
              </View>
              {formErrors.password && (
                <Text style={styles.errorText}>{formErrors.password}</Text>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Confirm Password</Text>
              <View style={styles.inputContainer}>
                <MaterialIcons
                  name='lock'
                  size={20}
                  color={COLORS.LIGHT_TEXT}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  value={formData.confirmPassword}
                  onChangeText={(text) =>
                    handleInputChange("confirmPassword", text)
                  }
                  placeholder='Confirm your password'
                  secureTextEntry
                />
              </View>
              {formErrors.confirmPassword && (
                <Text style={styles.errorText}>
                  {formErrors.confirmPassword}
                </Text>
              )}
            </View>

            {/* Address */}
            <Text style={styles.sectionTitle}>Address</Text>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Street Address</Text>
              <View style={styles.inputContainer}>
                <MaterialIcons
                  name='home'
                  size={20}
                  color={COLORS.LIGHT_TEXT}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  value={formData.address.street}
                  onChangeText={(text) =>
                    handleInputChange("address.street", text)
                  }
                  placeholder='Enter your street address'
                />
              </View>
              {formErrors["address.street"] && (
                <Text style={styles.errorText}>
                  {formErrors["address.street"]}
                </Text>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>City</Text>
              <View style={styles.inputContainer}>
                <MaterialIcons
                  name='location-city'
                  size={20}
                  color={COLORS.LIGHT_TEXT}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  value={formData.address.city}
                  onChangeText={(text) =>
                    handleInputChange("address.city", text)
                  }
                  placeholder='Enter your city'
                />
              </View>
              {formErrors["address.city"] && (
                <Text style={styles.errorText}>
                  {formErrors["address.city"]}
                </Text>
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Region</Text>
              <View style={styles.inputContainer}>
                <MaterialIcons
                  name='map'
                  size={20}
                  color={COLORS.LIGHT_TEXT}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  value={formData.address.region}
                  onChangeText={(text) =>
                    handleInputChange("address.region", text)
                  }
                  placeholder='Enter your region'
                />
              </View>
              {formErrors["address.region"] && (
                <Text style={styles.errorText}>
                  {formErrors["address.region"]}
                </Text>
              )}
            </View>

            {error && <Text style={styles.errorText}>{error}</Text>}

            <TouchableOpacity
              style={styles.registerButton}
              onPress={handleRegister}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color='#FFFFFF' size='small' />
              ) : (
                <Text style={styles.registerButtonText}>Register</Text>
              )}
            </TouchableOpacity>

            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>Already have an account?</Text>
              <TouchableOpacity onPress={handleLogin}>
                <Text style={styles.loginLink}>Login</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginTop: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: COLORS.TEXT,
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.LIGHT_TEXT,
  },
  form: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.TEXT,
    marginTop: 10,
    marginBottom: 15,
  },
  inputGroup: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 16,
    color: COLORS.TEXT,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 5,
    paddingHorizontal: 10,
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: 14,
    marginTop: 5,
  },
  registerButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: 12,
    borderRadius: 5,
    alignItems: "center",
    marginTop: 20,
  },
  registerButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  loginContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
  },
  loginText: {
    fontSize: 16,
    color: COLORS.LIGHT_TEXT,
  },
  loginLink: {
    fontSize: 16,
    color: COLORS.PRIMARY,
    fontWeight: "600",
    marginLeft: 5,
  },
});

export default RegisterScreen;

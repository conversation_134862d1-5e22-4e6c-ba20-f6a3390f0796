import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import { COLORS, ROUTES } from '../utils/constants';
import { useVoice } from '../context/VoiceContext';
import { useCart } from '../context/CartContext';

const ProductCard = ({ product, horizontal = false }) => {
  const navigation = useNavigation();
  const { selectProduct, speak } = useVoice();
  const { addItem } = useCart();
  
  if (!product) return null;
  
  const handlePress = () => {
    try {
      // Select the product for voice commands
      selectProduct(product);
      
      // Navigate to product details
      if (product && ROUTES.PRODUCT_DETAILS) {
        navigation.navigate(ROUTES.PRODUCT_DETAILS, { product });
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };
  
  const handleAddToCart = async () => {
    try {
      await addItem(product, 1);
      speak(`Added ${product.name} to cart`);
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  };
  
  return (
    <TouchableOpacity
      style={[styles.container, horizontal && styles.horizontalContainer]}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <Image
        source={{ uri: product.imageUrl }}
        style={[styles.image, horizontal && styles.horizontalImage]}
        resizeMode="cover"
      />
      
      <View style={[styles.content, horizontal && styles.horizontalContent]}>
        <Text style={styles.name} numberOfLines={2}>{product.name}</Text>
        <Text style={styles.price}>{product.currency} {product.price.toFixed(2)}</Text>
        
        <View style={styles.footer}>
          {!product.inStock && (
            <Text style={styles.outOfStock}>Out of Stock</Text>
          )}
          
          {product.inStock && (
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleAddToCart}
              activeOpacity={0.8}
            >
              <MaterialIcons name="add-shopping-cart" size={18} color="#FFFFFF" />
              <Text style={styles.addButtonText}>Add</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    overflow: 'hidden',
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    width: '48%',
  },
  horizontalContainer: {
    flexDirection: 'row',
    width: '100%',
  },
  image: {
    width: '100%',
    height: 120,
  },
  horizontalImage: {
    width: 100,
    height: 100,
  },
  content: {
    padding: 10,
  },
  horizontalContent: {
    flex: 1,
    justifyContent: 'center',
  },
  name: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 5,
    color: COLORS.TEXT,
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  outOfStock: {
    color: COLORS.ERROR,
    fontSize: 12,
    fontWeight: '500',
  },
  addButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
});

export default ProductCard; 
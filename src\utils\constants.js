// App-wide constants

// Voice command keywords
export const VOICE_COMMANDS = {
  SEARCH: ['search', 'find', 'look for', 'show me'],
  ADD_TO_CART: ['add', 'put in cart', 'buy', 'purchase'],
  REMOVE_FROM_CART: ['remove', 'delete', 'take out'],
  VIEW_CART: ['view cart', 'show cart', 'see cart', 'my cart'],
  CHECKOUT: ['checkout', 'check out', 'proceed to checkout', 'pay', 'payment'],
  CONFIRM: ['confirm', 'yes', 'proceed', 'continue'],
  CANCEL: ['cancel', 'no', 'stop', 'back'],
  QUANTITY: ['quantity', 'change quantity', 'set quantity'],
  NAVIGATE: ['go to', 'open', 'show', 'navigate to'],
};

// App routes
export const ROUTES = {
  HOME: 'Home',
  SEARCH: 'Search',
  PRODUCT_DETAILS: 'ProductDetails',
  CART: 'Cart',
  CHECKOUT: 'Checkout',
  PAYMENT: 'Payment',
  SUCCESS: 'Success',
  PROFILE: 'Profile',
  LOGIN: 'Login',
  REGISTER: 'Register',
};

// API endpoints (placeholder for now)
export const API = {
  PRODUCTS: 'https://api.example.com/products',
  CATEGORIES: 'https://api.example.com/categories',
};

// App colors
export const COLORS = {
  PRIMARY: '#5956E9',
  SECONDARY: '#F26E6E',
  ACCENT: '#FFCA42',
  BACKGROUND: '#FFFFFF',
  TEXT: '#2B2B2B',
  LIGHT_TEXT: '#7A7A7A',
  BORDER: '#E3E3E3',
  SUCCESS: '#4CAF50',
  ERROR: '#F44336',
  WARNING: '#FFC107',
}; 
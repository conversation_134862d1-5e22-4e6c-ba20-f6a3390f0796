import paystackConfig from '../config/paystack';
import authService from './authService';
import cartService from './cartService';

class PaymentService {
  constructor() {
    this.listeners = [];
  }
  
  // Generate payment reference
  _generateReference() {
    const date = new Date();
    return `IHV-${date.getFullYear()}${date.getMonth() + 1}${date.getDate()}-${Math.floor(Math.random() * 1000000)}`;
  }
  
  // Get payment configuration for Paystack
  getPaymentConfig(amount, email, callback) {
    // Get current user
    const user = authService.getCurrentUser();
    
    // Use user email if available, otherwise use provided email
    const userEmail = user ? user.email : email;
    
    if (!userEmail) {
      throw new Error('Email is required for payment');
    }
    
    // Generate payment reference
    const reference = this._generateReference();
    
    // Create payment config
    const paymentConfig = {
      publicKey: paystackConfig.publicKey,
      amount: amount * 100, // Paystack requires amount in kobo (pesewas for Ghana)
      email: userEmail,
      currency: paystackConfig.currency,
      reference,
      channels: paystackConfig.channels,
      
      // Optional metadata
      metadata: {
        custom_fields: [
          {
            display_name: "App Name",
            variable_name: "app_name",
            value: "IHearVoices"
          },
          {
            display_name: "Cart Items",
            variable_name: "cart_items",
            value: cartService.getCart().items.length.toString()
          }
        ]
      },
      
      // Callback functions
      onSuccess: (response) => {
        this._handlePaymentSuccess(response, callback);
      },
      onCancel: () => {
        this._handlePaymentCancel(callback);
      },
      onError: (error) => {
        this._handlePaymentError(error, callback);
      }
    };
    
    return paymentConfig;
  }
  
  // Handle successful payment
  _handlePaymentSuccess(response, callback) {
    // Create order object with payment details
    const order = {
      id: response.reference,
      date: new Date().toISOString(),
      amount: response.amount / 100, // Convert back from kobo/pesewas
      currency: paystackConfig.currency,
      status: 'completed',
      paymentMethod: response.channel || 'paystack',
      transactionId: response.transaction,
      items: cartService.getCart().items,
      customer: authService.getCurrentUser(),
    };
    
    // In a real app, this would save the order to a database
    
    // Notify listeners
    this._notifyListeners({
      type: 'payment_success',
      order
    });
    
    // Call the provided callback
    if (callback && typeof callback === 'function') {
      callback({
        success: true,
        order
      });
    }
    
    // Clear cart after successful payment
    cartService.clearCart();
  }
  
  // Handle payment cancellation
  _handlePaymentCancel(callback) {
    // Notify listeners
    this._notifyListeners({
      type: 'payment_cancelled'
    });
    
    // Call the provided callback
    if (callback && typeof callback === 'function') {
      callback({
        success: false,
        cancelled: true,
        message: 'Payment was cancelled'
      });
    }
  }
  
  // Handle payment error
  _handlePaymentError(error, callback) {
    console.error('Payment error:', error);
    
    // Notify listeners
    this._notifyListeners({
      type: 'payment_error',
      error
    });
    
    // Call the provided callback
    if (callback && typeof callback === 'function') {
      callback({
        success: false,
        error: true,
        message: error.message || 'Payment failed'
      });
    }
  }
  
  // Add a listener for payment events
  addListener(callback) {
    this.listeners.push(callback);
    return this.listeners.length - 1; // Return the index for removal
  }
  
  // Remove a listener
  removeListener(index) {
    if (index >= 0 && index < this.listeners.length) {
      this.listeners.splice(index, 1);
    }
  }
  
  // Notify all listeners of payment events
  _notifyListeners(event) {
    this.listeners.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('Error in payment listener:', error);
      }
    });
  }
}

// Create and export a singleton instance
const paymentService = new PaymentService();
export default paymentService; 
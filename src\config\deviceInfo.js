import * as Device from 'expo-device';
import { Platform } from 'react-native';

// Export the device module
export default Device;

// Helper functions for common device info needs
export const getDeviceType = () => {
  try {
    return Device.DeviceType[Device.deviceType] || 'UNKNOWN';
  } catch (error) {
    console.warn('Error getting device type:', error);
    return 'UNKNOWN';
  }
};

export const getDeviceInfo = async () => {
  try {
    return {
      brand: Device.brand,
      manufacturer: Device.manufacturer,
      modelName: Device.modelName,
      designName: Device.designName,
      productName: Device.productName,
      deviceYearClass: Device.deviceYearClass,
      totalMemory: Device.totalMemory,
      osName: Device.osName,
      osVersion: Device.osVersion,
      osBuildId: Device.osBuildId,
      osInternalBuildId: Device.osInternalBuildId,
      platformApiLevel: Device.platformApiLevel,
      deviceName: await Device.getDeviceNameAsync() || 'Unknown Device',
    };
  } catch (error) {
    console.warn('Error getting device info:', error);
    return {
      platform: Platform.OS,
      version: Platform.Version,
    };
  }
};

export const isTablet = () => {
  try {
    return Device.deviceType === Device.DeviceType.TABLET;
  } catch (error) {
    console.warn('Error checking if device is tablet:', error);
    return false;
  }
};

export const isPhone = () => {
  try {
    return Device.deviceType === Device.DeviceType.PHONE;
  } catch (error) {
    console.warn('Error checking if device is phone:', error);
    return true; // Default to phone if error
  }
};

export const getPlatformInfo = () => {
  return {
    os: Platform.OS,
    version: Platform.Version,
  };
}; 
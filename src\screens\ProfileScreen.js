import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, ROUTES } from "../utils/constants";
import VoiceButton from "../components/VoiceButton";
import TranscriptDisplay from "../components/TranscriptDisplay";
import { useAuth } from "../context/AuthContext";
import { useVoice } from "../context/VoiceContext";

const ProfileScreen = ({ navigation }) => {
  const { user, updateProfile, logout, loading } = useAuth();
  const { speak } = useVoice();

  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || "",
    phone: user?.phone || "",
    address: {
      street: user?.address?.street || "",
      city: user?.address?.city || "",
      region: user?.address?.region || "",
      country: user?.address?.country || "Ghana",
    },
  });

  const [formErrors, setFormErrors] = useState({});

  // Handle input change
  const handleInputChange = (field, value) => {
    if (field.includes(".")) {
      // Handle nested fields (address)
      const [parent, child] = field.split(".");
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent],
          [child]: value,
        },
      });
    } else {
      // Handle top-level fields
      setFormData({
        ...formData,
        [field]: value,
      });
    }

    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors({
        ...formErrors,
        [field]: null,
      });
    }
  };

  // Validate form
  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = "Name is required";
    }

    if (!formData.phone.trim()) {
      errors.phone = "Phone number is required";
    }

    if (!formData.address.street.trim()) {
      errors["address.street"] = "Street address is required";
    }

    if (!formData.address.city.trim()) {
      errors["address.city"] = "City is required";
    }

    if (!formData.address.region.trim()) {
      errors["address.region"] = "Region is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle save profile
  const handleSaveProfile = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      await updateProfile(formData);
      setIsEditing(false);
      speak("Profile updated successfully");
      Alert.alert("Success", "Profile updated successfully");
    } catch (error) {
      Alert.alert("Error", error.message);
    }
  };

  // Handle logout
  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to logout?", [
      {
        text: "Cancel",
        style: "cancel",
      },
      {
        text: "Logout",
        onPress: async () => {
          await logout();
          speak("You have been logged out");
          navigation.navigate(ROUTES.HOME);
        },
      },
    ]);
  };

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.notLoggedInContainer}>
          <MaterialIcons
            name='account-circle'
            size={80}
            color={COLORS.LIGHT_TEXT}
          />
          <Text style={styles.notLoggedInText}>You are not logged in</Text>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigation.navigate(ROUTES.LOGIN)}
          >
            <Text style={styles.loginButtonText}>Login</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.registerButton}
            onPress={() => navigation.navigate(ROUTES.REGISTER)}
          >
            <Text style={styles.registerButtonText}>Register</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>My Profile</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setIsEditing(!isEditing)}
            >
              <MaterialIcons
                name={isEditing ? "close" : "edit"}
                size={24}
                color={COLORS.PRIMARY}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.profileContainer}>
            <View style={styles.avatarContainer}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  {user.name ? user.name.charAt(0).toUpperCase() : "U"}
                </Text>
              </View>
              <Text style={styles.name}>{user.name}</Text>
              <Text style={styles.email}>{user.email}</Text>
            </View>

            <View style={styles.infoContainer}>
              {isEditing ? (
                // Edit Form
                <>
                  <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Full Name</Text>
                    <TextInput
                      style={[
                        styles.input,
                        formErrors.name && styles.inputError,
                      ]}
                      value={formData.name}
                      onChangeText={(text) => handleInputChange("name", text)}
                      placeholder='Enter your full name'
                    />
                    {formErrors.name && (
                      <Text style={styles.errorText}>{formErrors.name}</Text>
                    )}
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Phone Number</Text>
                    <TextInput
                      style={[
                        styles.input,
                        formErrors.phone && styles.inputError,
                      ]}
                      value={formData.phone}
                      onChangeText={(text) => handleInputChange("phone", text)}
                      placeholder='Enter your phone number'
                      keyboardType='phone-pad'
                    />
                    {formErrors.phone && (
                      <Text style={styles.errorText}>{formErrors.phone}</Text>
                    )}
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Street Address</Text>
                    <TextInput
                      style={[
                        styles.input,
                        formErrors["address.street"] && styles.inputError,
                      ]}
                      value={formData.address.street}
                      onChangeText={(text) =>
                        handleInputChange("address.street", text)
                      }
                      placeholder='Enter your street address'
                    />
                    {formErrors["address.street"] && (
                      <Text style={styles.errorText}>
                        {formErrors["address.street"]}
                      </Text>
                    )}
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>City</Text>
                    <TextInput
                      style={[
                        styles.input,
                        formErrors["address.city"] && styles.inputError,
                      ]}
                      value={formData.address.city}
                      onChangeText={(text) =>
                        handleInputChange("address.city", text)
                      }
                      placeholder='Enter your city'
                    />
                    {formErrors["address.city"] && (
                      <Text style={styles.errorText}>
                        {formErrors["address.city"]}
                      </Text>
                    )}
                  </View>

                  <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Region</Text>
                    <TextInput
                      style={[
                        styles.input,
                        formErrors["address.region"] && styles.inputError,
                      ]}
                      value={formData.address.region}
                      onChangeText={(text) =>
                        handleInputChange("address.region", text)
                      }
                      placeholder='Enter your region'
                    />
                    {formErrors["address.region"] && (
                      <Text style={styles.errorText}>
                        {formErrors["address.region"]}
                      </Text>
                    )}
                  </View>

                  <TouchableOpacity
                    style={styles.saveButton}
                    onPress={handleSaveProfile}
                    disabled={loading}
                  >
                    {loading ? (
                      <ActivityIndicator color='#FFFFFF' size='small' />
                    ) : (
                      <Text style={styles.saveButtonText}>Save Changes</Text>
                    )}
                  </TouchableOpacity>
                </>
              ) : (
                // Display Info
                <>
                  <View style={styles.infoRow}>
                    <MaterialIcons
                      name='phone'
                      size={20}
                      color={COLORS.LIGHT_TEXT}
                    />
                    <View style={styles.infoTextContainer}>
                      <Text style={styles.infoLabel}>Phone</Text>
                      <Text style={styles.infoValue}>
                        {user.phone || "Not provided"}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.infoRow}>
                    <MaterialIcons
                      name='home'
                      size={20}
                      color={COLORS.LIGHT_TEXT}
                    />
                    <View style={styles.infoTextContainer}>
                      <Text style={styles.infoLabel}>Address</Text>
                      <Text style={styles.infoValue}>
                        {user.address?.street
                          ? `${user.address.street}, ${user.address.city}, ${user.address.region}`
                          : "Not provided"}
                      </Text>
                    </View>
                  </View>
                </>
              )}
            </View>
          </View>

          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <MaterialIcons name='logout' size={20} color='#FFFFFF' />
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Voice Button */}
      <View style={styles.voiceButtonContainer}>
        <VoiceButton />
      </View>

      {/* Transcript Display */}
      <TranscriptDisplay />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: COLORS.TEXT,
  },
  editButton: {
    padding: 5,
  },
  profileContainer: {
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  avatarContainer: {
    alignItems: "center",
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.PRIMARY,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  name: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.TEXT,
    marginBottom: 5,
  },
  email: {
    fontSize: 16,
    color: COLORS.LIGHT_TEXT,
  },
  infoContainer: {
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 15,
  },
  infoTextContainer: {
    marginLeft: 10,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: COLORS.LIGHT_TEXT,
    marginBottom: 3,
  },
  infoValue: {
    fontSize: 16,
    color: COLORS.TEXT,
  },
  inputGroup: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 16,
    color: COLORS.TEXT,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingVertical: 12,
    fontSize: 16,
  },
  inputError: {
    borderColor: COLORS.ERROR,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: 14,
    marginTop: 5,
  },
  saveButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: 12,
    borderRadius: 5,
    alignItems: "center",
    marginTop: 10,
  },
  saveButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  logoutButton: {
    backgroundColor: COLORS.SECONDARY,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 12,
    borderRadius: 5,
    marginTop: 10,
  },
  logoutButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 5,
  },
  notLoggedInContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  notLoggedInText: {
    fontSize: 18,
    color: COLORS.LIGHT_TEXT,
    marginTop: 10,
    marginBottom: 20,
  },
  loginButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 5,
    marginBottom: 10,
    width: "80%",
    alignItems: "center",
  },
  loginButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  registerButton: {
    backgroundColor: "#FFFFFF",
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: COLORS.PRIMARY,
    width: "80%",
    alignItems: "center",
  },
  registerButtonText: {
    color: COLORS.PRIMARY,
    fontSize: 16,
    fontWeight: "600",
  },
  voiceButtonContainer: {
    position: "absolute",
    bottom: 20,
    right: 20,
  },
});

export default ProfileScreen;

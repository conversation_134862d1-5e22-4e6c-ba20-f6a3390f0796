import React, { useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, ROUTES } from "../utils/constants";
import CartItem from "../components/CartItem";
import VoiceButton from "../components/VoiceButton";
import TranscriptDisplay from "../components/TranscriptDisplay";
import { useVoice } from "../context/VoiceContext";
import { useCart } from "../context/CartContext";
import { useVoiceNavigation } from "../hooks/useVoiceNavigation";

const CartScreen = ({ navigation }) => {
  const { speak } = useVoice();
  const { cart, loading, clearCart, getTotal, hasItems } = useCart();

  // Set up voice navigation
  useVoiceNavigation();

  // Announce cart status when screen loads
  useEffect(() => {
    if (cart.items.length > 0) {
      speak(
        `You have ${cart.items.length} items in your cart. Total: ${cart.total} Ghana cedis.`
      );
    } else {
      speak(
        'Your cart is empty. Say "search for" followed by a product name to start shopping.'
      );
    }
  }, []);

  // Handle checkout
  const handleCheckout = () => {
    navigation.navigate(ROUTES.CHECKOUT);
  };

  // Handle clear cart
  const handleClearCart = () => {
    clearCart();
    speak("Cart cleared");
  };

  // Render cart item
  const renderCartItem = ({ item }) => <CartItem item={item} />;

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Cart</Text>
        {hasItems() && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClearCart}
          >
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        )}
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={COLORS.PRIMARY} />
        </View>
      ) : hasItems() ? (
        <>
          <FlatList
            data={cart.items}
            renderItem={renderCartItem}
            keyExtractor={(item) => item.product.id}
            contentContainerStyle={styles.cartList}
            showsVerticalScrollIndicator={false}
          />

          <View style={styles.summaryContainer}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal</Text>
              <Text style={styles.summaryValue}>
                {cart.items[0]?.product.currency || "GHS"}{" "}
                {getTotal().toFixed(2)}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Delivery</Text>
              <Text style={styles.summaryValue}>GHS 10.00</Text>
            </View>

            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>
                {cart.items[0]?.product.currency || "GHS"}{" "}
                {(getTotal() + 10).toFixed(2)}
              </Text>
            </View>

            <TouchableOpacity
              style={styles.checkoutButton}
              onPress={handleCheckout}
            >
              <Text style={styles.checkoutButtonText}>Proceed to Checkout</Text>
              <MaterialIcons name='arrow-forward' size={20} color='#FFFFFF' />
            </TouchableOpacity>
          </View>
        </>
      ) : (
        <View style={styles.emptyContainer}>
          <MaterialIcons
            name='shopping-cart'
            size={64}
            color={COLORS.LIGHT_TEXT}
          />
          <Text style={styles.emptyText}>Your cart is empty</Text>
          <TouchableOpacity
            style={styles.shopButton}
            onPress={() => navigation.navigate(ROUTES.HOME)}
          >
            <Text style={styles.shopButtonText}>Start Shopping</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Voice Button */}
      <View style={styles.voiceButtonContainer}>
        <VoiceButton />
      </View>

      {/* Transcript Display */}
      <TranscriptDisplay />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 15,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.TEXT,
  },
  clearButton: {
    padding: 5,
  },
  clearButtonText: {
    color: COLORS.SECONDARY,
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  cartList: {
    padding: 15,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: COLORS.LIGHT_TEXT,
    marginTop: 15,
    marginBottom: 20,
  },
  shopButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
  },
  shopButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  summaryContainer: {
    backgroundColor: "#FFFFFF",
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  summaryLabel: {
    fontSize: 16,
    color: COLORS.LIGHT_TEXT,
  },
  summaryValue: {
    fontSize: 16,
    color: COLORS.TEXT,
    fontWeight: "500",
  },
  totalRow: {
    marginTop: 5,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    marginBottom: 15,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.TEXT,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.PRIMARY,
  },
  checkoutButton: {
    backgroundColor: COLORS.PRIMARY,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 12,
    borderRadius: 5,
  },
  checkoutButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    marginRight: 5,
  },
  voiceButtonContainer: {
    position: "absolute",
    bottom: 20,
    right: 20,
  },
});

export default CartScreen;

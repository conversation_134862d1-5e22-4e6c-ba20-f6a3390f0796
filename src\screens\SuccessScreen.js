import React, { useEffect } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, ROUTES } from "../utils/constants";
import { useVoice } from "../context/VoiceContext";

const SuccessScreen = ({ route, navigation }) => {
  const { order } = route.params || {};
  const { speak } = useVoice();

  // Announce success message
  useEffect(() => {
    speak(
      "Your order has been successfully placed. Thank you for shopping with I Hear Voices."
    );
  }, []);

  // Handle continue shopping
  const handleContinueShopping = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: ROUTES.HOME }],
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <MaterialIcons
            name='check-circle'
            size={100}
            color={COLORS.SUCCESS}
          />
        </View>

        <Text style={styles.title}>Order Successful!</Text>
        <Text style={styles.message}>
          Your order has been successfully placed and will be processed shortly.
        </Text>

        {order && (
          <View style={styles.orderInfo}>
            <Text style={styles.orderInfoTitle}>Order Details</Text>

            <View style={styles.orderInfoRow}>
              <Text style={styles.orderInfoLabel}>Order ID</Text>
              <Text style={styles.orderInfoValue}>{order.id}</Text>
            </View>

            <View style={styles.orderInfoRow}>
              <Text style={styles.orderInfoLabel}>Date</Text>
              <Text style={styles.orderInfoValue}>
                {new Date(order.date).toLocaleDateString()}
              </Text>
            </View>

            <View style={styles.orderInfoRow}>
              <Text style={styles.orderInfoLabel}>Amount</Text>
              <Text style={styles.orderInfoValue}>
                {order.currency} {order.amount.toFixed(2)}
              </Text>
            </View>

            <View style={styles.orderInfoRow}>
              <Text style={styles.orderInfoLabel}>Payment Method</Text>
              <Text style={styles.orderInfoValue}>{order.paymentMethod}</Text>
            </View>
          </View>
        )}

        <TouchableOpacity
          style={styles.button}
          onPress={handleContinueShopping}
        >
          <Text style={styles.buttonText}>Continue Shopping</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  iconContainer: {
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: COLORS.TEXT,
    marginBottom: 10,
  },
  message: {
    fontSize: 16,
    color: COLORS.LIGHT_TEXT,
    textAlign: "center",
    marginBottom: 30,
  },
  orderInfo: {
    width: "100%",
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    padding: 15,
    marginBottom: 30,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  orderInfoTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.TEXT,
    marginBottom: 15,
  },
  orderInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  orderInfoLabel: {
    fontSize: 16,
    color: COLORS.LIGHT_TEXT,
  },
  orderInfoValue: {
    fontSize: 16,
    color: COLORS.TEXT,
    fontWeight: "500",
  },
  button: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 5,
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default SuccessScreen;

import AsyncStorage from '@react-native-async-storage/async-storage';

const CART_STORAGE_KEY = '@ihearvoices_cart';

class CartService {
  constructor() {
    this.cart = {
      items: [],
      total: 0,
    };
    this.listeners = [];
    
    // Load cart from storage when service is initialized
    this.loadCart();
  }
  
  // Load cart from AsyncStorage
  async loadCart() {
    try {
      const cartData = await AsyncStorage.getItem(CART_STORAGE_KEY);
      if (cartData) {
        this.cart = JSON.parse(cartData);
        this._notifyListeners();
      }
      return this.cart;
    } catch (error) {
      console.error('Error loading cart from storage:', error);
      return this.cart;
    }
  }
  
  // Save cart to AsyncStorage
  async _saveCart() {
    try {
      await AsyncStorage.setItem(CART_STORAGE_KEY, JSON.stringify(this.cart));
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  }
  
  // Get current cart
  getCart() {
    return this.cart;
  }
  
  // Add item to cart
  async addItem(product, quantity = 1) {
    if (!product || !product.id) {
      throw new Error('Invalid product');
    }
    
    // Check if product already exists in cart
    const existingItemIndex = this.cart.items.findIndex(item => item.product.id === product.id);
    
    if (existingItemIndex >= 0) {
      // Update quantity if product already in cart
      this.cart.items[existingItemIndex].quantity += quantity;
    } else {
      // Add new item to cart
      this.cart.items.push({
        product,
        quantity
      });
    }
    
    // Recalculate total
    this._recalculateTotal();
    
    // Save to storage and notify listeners
    await this._saveCart();
    this._notifyListeners();
    
    return this.cart;
  }
  
  // Remove item from cart
  async removeItem(productId) {
    if (!productId) {
      throw new Error('Invalid product ID');
    }
    
    // Filter out the item with the given ID
    this.cart.items = this.cart.items.filter(item => item.product.id !== productId);
    
    // Recalculate total
    this._recalculateTotal();
    
    // Save to storage and notify listeners
    await this._saveCart();
    this._notifyListeners();
    
    return this.cart;
  }
  
  // Update item quantity
  async updateItemQuantity(productId, quantity) {
    if (!productId) {
      throw new Error('Invalid product ID');
    }
    
    if (quantity <= 0) {
      // If quantity is 0 or negative, remove the item
      return this.removeItem(productId);
    }
    
    // Find the item and update quantity
    const itemIndex = this.cart.items.findIndex(item => item.product.id === productId);
    
    if (itemIndex >= 0) {
      this.cart.items[itemIndex].quantity = quantity;
      
      // Recalculate total
      this._recalculateTotal();
      
      // Save to storage and notify listeners
      await this._saveCart();
      this._notifyListeners();
    }
    
    return this.cart;
  }
  
  // Clear the entire cart
  async clearCart() {
    this.cart = {
      items: [],
      total: 0,
    };
    
    // Save to storage and notify listeners
    await this._saveCart();
    this._notifyListeners();
    
    return this.cart;
  }
  
  // Recalculate cart total
  _recalculateTotal() {
    this.cart.total = this.cart.items.reduce((total, item) => {
      return total + (item.product.price * item.quantity);
    }, 0);
  }
  
  // Add a listener for cart changes
  addListener(callback) {
    this.listeners.push(callback);
    return this.listeners.length - 1; // Return the index for removal
  }
  
  // Remove a listener
  removeListener(index) {
    if (index >= 0 && index < this.listeners.length) {
      this.listeners.splice(index, 1);
    }
  }
  
  // Notify all listeners of cart changes
  _notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.cart);
      } catch (error) {
        console.error('Error in cart listener:', error);
      }
    });
  }
}

// Create and export a singleton instance
const cartService = new CartService();
export default cartService; 
import { API } from '../utils/constants';

// Mock product data for development
const mockProducts = [
  {
    id: '1',
    name: 'Black Running Shoes',
    description: 'Comfortable running shoes for everyday use',
    price: 120,
    currency: 'GHS',
    imageUrl: 'https://via.placeholder.com/300x300?text=Black+Shoes',
    category: 'shoes',
    tags: ['black', 'running', 'shoes', 'sports'],
    inStock: true,
  },
  {
    id: '2',
    name: 'Blue Denim Jeans',
    description: 'Classic blue jeans with a modern fit',
    price: 85,
    currency: 'GHS',
    imageUrl: 'https://via.placeholder.com/300x300?text=Blue+Jeans',
    category: 'clothing',
    tags: ['blue', 'denim', 'jeans', 'pants'],
    inStock: true,
  },
  {
    id: '3',
    name: 'White T-Shirt',
    description: 'Plain white t-shirt made from premium cotton',
    price: 45,
    currency: 'GHS',
    imageUrl: 'https://via.placeholder.com/300x300?text=White+Tshirt',
    category: 'clothing',
    tags: ['white', 'tshirt', 't-shirt', 'cotton'],
    inStock: true,
  },
  {
    id: '4',
    name: 'Leather Wallet',
    description: 'Genuine leather wallet with multiple card slots',
    price: 65,
    currency: 'GHS',
    imageUrl: 'https://via.placeholder.com/300x300?text=Leather+Wallet',
    category: 'accessories',
    tags: ['leather', 'wallet', 'brown', 'accessories'],
    inStock: true,
  },
  {
    id: '5',
    name: 'Smartphone Case',
    description: 'Protective case for the latest smartphone models',
    price: 35,
    currency: 'GHS',
    imageUrl: 'https://via.placeholder.com/300x300?text=Phone+Case',
    category: 'accessories',
    tags: ['phone', 'case', 'smartphone', 'protection'],
    inStock: false,
  },
];

class ProductService {
  // Get all products
  async getProducts() {
    try {
      // In a real app, this would be an API call
      // return await fetch(API.PRODUCTS).then(res => res.json());
      
      // For now, return mock data
      return mockProducts;
    } catch (error) {
      console.error('Error fetching products:', error);
      return [];
    }
  }
  
  // Get a single product by ID
  async getProductById(id) {
    try {
      // In a real app, this would be an API call
      // return await fetch(`${API.PRODUCTS}/${id}`).then(res => res.json());
      
      // For now, return from mock data
      return mockProducts.find(product => product.id === id) || null;
    } catch (error) {
      console.error(`Error fetching product with ID ${id}:`, error);
      return null;
    }
  }
  
  // Search products by query string
  async searchProducts(query) {
    try {
      // In a real app, this would be an API call with search parameters
      // return await fetch(`${API.PRODUCTS}/search?q=${encodeURIComponent(query)}`).then(res => res.json());
      
      // For now, search mock data
      if (!query) return [];
      
      const lowerQuery = query.toLowerCase();
      return mockProducts.filter(product => {
        // Search in name, description, and tags
        return (
          product.name.toLowerCase().includes(lowerQuery) ||
          product.description.toLowerCase().includes(lowerQuery) ||
          product.category.toLowerCase().includes(lowerQuery) ||
          product.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
        );
      });
    } catch (error) {
      console.error(`Error searching products with query "${query}":`, error);
      return [];
    }
  }
  
  // Get products by category
  async getProductsByCategory(category) {
    try {
      // In a real app, this would be an API call with category filter
      // return await fetch(`${API.PRODUCTS}/category/${category}`).then(res => res.json());
      
      // For now, filter mock data
      return mockProducts.filter(
        product => product.category.toLowerCase() === category.toLowerCase()
      );
    } catch (error) {
      console.error(`Error fetching products in category "${category}":`, error);
      return [];
    }
  }
  
  // Get available categories
  async getCategories() {
    try {
      // In a real app, this would be an API call
      // return await fetch(API.CATEGORIES).then(res => res.json());
      
      // For now, extract unique categories from mock data
      const categories = [...new Set(mockProducts.map(product => product.category))];
      return categories.map(category => ({
        id: category,
        name: category.charAt(0).toUpperCase() + category.slice(1),
      }));
    } catch (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
  }
}

// Create and export a singleton instance
const productService = new ProductService();
export default productService; 
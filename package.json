{"name": "ihearvoices", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-av": "^15.1.7", "expo-device": "~7.1.4", "expo-file-system": "^18.1.11", "expo-font": "^13.3.2", "expo-linking": "^7.1.7", "expo-speech": "^13.1.7", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-flutterwave": "^1.0.0", "react-native-gesture-handler": "~2.24.0", "react-native-paystack-webview": "^5.0.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}